<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:background="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- Item Image -->
        <ImageView
            android:id="@+id/item_image"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="12dp"
            android:scaleType="centerCrop"
            android:background="@drawable/image_background"
            tools:src="@drawable/placeholder_image" />

        <!-- Item <PERSON> -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_gravity="center_vertical">

            <!-- Item Name -->
            <TextView
                android:id="@+id/item_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="Cappuccino" />

            <!-- Item Description -->
            <TextView
                android:id="@+id/item_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="Espresso with steamed milk and foam" />

            <!-- Item Price -->
            <TextView
                android:id="@+id/item_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/price_color"
                tools:text="$4.25" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
