package com.cafe.menu.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.cafe.menu.R
import com.cafe.menu.model.MenuItem
import java.text.NumberFormat
import java.util.*

class MenuAdapter(private var menuItems: List<MenuItem>) : 
    RecyclerView.Adapter<MenuAdapter.MenuViewHolder>() {

    class MenuViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val itemImage: ImageView = itemView.findViewById(R.id.item_image)
        val itemName: TextView = itemView.findViewById(R.id.item_name)
        val itemDescription: TextView = itemView.findViewById(R.id.item_description)
        val itemPrice: TextView = itemView.findViewById(R.id.item_price)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_menu, parent, false)
        return MenuViewHolder(view)
    }

    override fun onBindViewHolder(holder: MenuViewHolder, position: Int) {
        val menuItem = menuItems[position]
        
        holder.itemName.text = menuItem.name
        holder.itemDescription.text = menuItem.description
        
        // Format price as currency
        val currencyFormat = NumberFormat.getCurrencyInstance(Locale.US)
        holder.itemPrice.text = currencyFormat.format(menuItem.price)
        
        // Load image using Glide
        Glide.with(holder.itemView.context)
            .load(menuItem.imageUrl)
            .placeholder(R.drawable.placeholder_image)
            .error(R.drawable.placeholder_image)
            .centerCrop()
            .into(holder.itemImage)
    }

    override fun getItemCount(): Int = menuItems.size

    fun updateItems(newItems: List<MenuItem>) {
        menuItems = newItems
        notifyDataSetChanged()
    }
}
