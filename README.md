# ☕ Coffee Menu App

A simple and elegant Android application built with Kotlin that displays a digital menu for cafés, restaurants, and coffee shops.

## 🌟 Features

- **Digital Menu Display**: Clean, modern interface showing menu items with images, descriptions, and prices
- **Category Organization**: Items organized into Hot Drinks, Cold Drinks, Desserts, and Food
- **Beautiful Item Cards**: Each menu item displayed in attractive cards with images
- **Tab Navigation**: Easy switching between different menu categories
- **Offline Ready**: Works without internet once images are cached
- **Material Design**: Modern UI following Material Design guidelines

## 🛠️ Technical Details

- **Language**: Kotlin
- **Minimum SDK**: API 25 (Android 7.1)
- **Target SDK**: API 34 (Android 14)
- **Architecture**: Simple MVC pattern
- **UI Components**: RecyclerView, CardView, TabLayout
- **Image Loading**: Glide library for efficient image loading and caching

## 📱 Screenshots

The app features:
- A toolbar with the app title
- Tab layout for category filtering
- RecyclerView displaying menu items in cards
- Each card shows item image, name, description, and price

## 🚀 Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK with API 25+
- Kotlin plugin

### Installation
1. Clone this repository
2. Open the project in Android Studio
3. Sync the project with Gradle files
4. Run the app on an emulator or physical device

### Building
```bash
./gradlew assembleDebug
```

### Running Tests
```bash
./gradlew test
```

## 📂 Project Structure

```
app/
├── src/main/java/com/cafe/menu/
│   ├── MainActivity.kt              # Main activity
│   ├── adapter/
│   │   └── MenuAdapter.kt          # RecyclerView adapter
│   ├── data/
│   │   └── MenuRepository.kt       # Data source
│   └── model/
│       └── MenuItem.kt             # Data models
├── src/main/res/
│   ├── layout/                     # XML layouts
│   ├── values/                     # Colors, strings, themes
│   └── drawable/                   # Drawable resources
└── build.gradle.kts               # App-level Gradle config
```

## 🎨 Customization

### Adding New Menu Items
Edit the `MenuRepository.kt` file to add, remove, or modify menu items:

```kotlin
MenuItem(
    id = 13,
    name = "Your Item Name",
    description = "Item description",
    price = 5.99,
    imageUrl = "https://your-image-url.com/image.jpg",
    category = MenuCategory.HOT_DRINKS
)
```

### Changing Colors
Modify colors in `app/src/main/res/values/colors.xml`:
- `primary_color`: Main app color (brown theme)
- `accent_color`: Accent color for highlights
- `price_color`: Color for price text

### Adding New Categories
1. Add new category to `MenuCategory` enum in `MenuItem.kt`
2. Update the tab setup in `MainActivity.kt`
3. Add corresponding string resources

## 📋 Dependencies

- AndroidX Core KTX
- AppCompat
- Material Design Components
- RecyclerView
- CardView
- Glide (for image loading)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🔮 Future Enhancements

- Search functionality
- Item favorites
- Price filtering
- Admin panel for menu management
- Order placement functionality
- Multiple language support
- Dark theme support
