package com.cafe.menu

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.cafe.menu.adapter.MenuAdapter
import com.cafe.menu.data.MenuRepository
import com.cafe.menu.model.MenuCategory
import com.google.android.material.tabs.TabLayout

class MainActivity : AppCompatActivity() {
    
    private lateinit var recyclerView: RecyclerView
    private lateinit var tabLayout: TabLayout
    private lateinit var menuAdapter: MenuAdapter
    private lateinit var menuRepository: MenuRepository
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        initializeViews()
        setupRecyclerView()
        setupTabs()
        loadAllMenuItems()
    }
    
    private fun initializeViews() {
        recyclerView = findViewById(R.id.recycler_view)
        tabLayout = findViewById(R.id.tab_layout)
        menuRepository = MenuRepository()
    }
    
    private fun setupRecyclerView() {
        menuAdapter = MenuAdapter(emptyList())
        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = menuAdapter
            setHasFixedSize(true)
        }
    }
    
    private fun setupTabs() {
        // Add "All Items" tab
        tabLayout.addTab(tabLayout.newTab().setText(getString(R.string.all_items)))
        
        // Add category tabs
        MenuCategory.values().forEach { category ->
            val tab = tabLayout.newTab().setText(category.displayName)
            tabLayout.addTab(tab)
        }
        
        // Set tab selection listener
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let { selectedTab ->
                    when (selectedTab.position) {
                        0 -> loadAllMenuItems() // All Items
                        1 -> loadMenuItemsByCategory(MenuCategory.HOT_DRINKS)
                        2 -> loadMenuItemsByCategory(MenuCategory.COLD_DRINKS)
                        3 -> loadMenuItemsByCategory(MenuCategory.DESSERTS)
                        4 -> loadMenuItemsByCategory(MenuCategory.FOOD)
                    }
                }
            }
            
            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }
    
    private fun loadAllMenuItems() {
        val allItems = menuRepository.getMenuItems()
        menuAdapter.updateItems(allItems)
    }
    
    private fun loadMenuItemsByCategory(category: MenuCategory) {
        val categoryItems = menuRepository.getMenuItemsByCategory(category)
        menuAdapter.updateItems(categoryItems)
    }
}
