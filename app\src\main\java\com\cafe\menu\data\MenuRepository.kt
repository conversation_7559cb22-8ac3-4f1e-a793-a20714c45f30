package com.cafe.menu.data

import com.cafe.menu.model.MenuCategory
import com.cafe.menu.model.MenuItem

class MenuRepository {
    
    fun getMenuItems(): List<MenuItem> {
        return listOf(
            // Hot Drinks
            MenuItem(
                id = 1,
                name = "Espresso",
                description = "Rich and bold single shot of espresso",
                price = 2.50,
                imageUrl = "https://images.unsplash.com/photo-1510707577719-ae7c14805e3a?w=400",
                category = MenuCategory.HOT_DRINKS
            ),
            MenuItem(
                id = 2,
                name = "Cappuccino",
                description = "Espresso with steamed milk and foam",
                price = 4.25,
                imageUrl = "https://images.unsplash.com/photo-1572442388796-11668a67e53d?w=400",
                category = MenuCategory.HOT_DRINKS
            ),
            MenuItem(
                id = 3,
                name = "Latte",
                description = "Smooth espresso with steamed milk",
                price = 4.75,
                imageUrl = "https://images.unsplash.com/photo-1561047029-3000c68339ca?w=400",
                category = MenuCategory.HOT_DRINKS
            ),
            MenuItem(
                id = 4,
                name = "Americano",
                description = "Espresso with hot water",
                price = 3.25,
                imageUrl = "https://images.unsplash.com/photo-1497636577773-f1231844b336?w=400",
                category = MenuCategory.HOT_DRINKS
            ),
            
            // Cold Drinks
            MenuItem(
                id = 5,
                name = "Iced Coffee",
                description = "Refreshing cold brew coffee over ice",
                price = 3.75,
                imageUrl = "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400",
                category = MenuCategory.COLD_DRINKS
            ),
            MenuItem(
                id = 6,
                name = "Frappuccino",
                description = "Blended coffee with ice and whipped cream",
                price = 5.25,
                imageUrl = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
                category = MenuCategory.COLD_DRINKS
            ),
            MenuItem(
                id = 7,
                name = "Cold Brew",
                description = "Smooth cold-brewed coffee",
                price = 4.00,
                imageUrl = "https://images.unsplash.com/photo-1517701604599-bb29b565090c?w=400",
                category = MenuCategory.COLD_DRINKS
            ),
            
            // Desserts
            MenuItem(
                id = 8,
                name = "Chocolate Cake",
                description = "Rich chocolate cake with ganache",
                price = 6.50,
                imageUrl = "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400",
                category = MenuCategory.DESSERTS
            ),
            MenuItem(
                id = 9,
                name = "Cheesecake",
                description = "Creamy New York style cheesecake",
                price = 5.75,
                imageUrl = "https://images.unsplash.com/photo-1533134242443-d4fd215305ad?w=400",
                category = MenuCategory.DESSERTS
            ),
            MenuItem(
                id = 10,
                name = "Croissant",
                description = "Buttery, flaky French pastry",
                price = 3.25,
                imageUrl = "https://images.unsplash.com/photo-1555507036-ab794f4afe5a?w=400",
                category = MenuCategory.DESSERTS
            ),
            
            // Food
            MenuItem(
                id = 11,
                name = "Avocado Toast",
                description = "Fresh avocado on artisan bread",
                price = 8.50,
                imageUrl = "https://images.unsplash.com/photo-1541519227354-08fa5d50c44d?w=400",
                category = MenuCategory.FOOD
            ),
            MenuItem(
                id = 12,
                name = "Breakfast Sandwich",
                description = "Egg, cheese, and bacon on a fresh bun",
                price = 7.25,
                imageUrl = "https://images.unsplash.com/photo-1481070555726-e2fe8357725c?w=400",
                category = MenuCategory.FOOD
            )
        )
    }
    
    fun getMenuItemsByCategory(category: MenuCategory): List<MenuItem> {
        return getMenuItems().filter { it.category == category }
    }
}
